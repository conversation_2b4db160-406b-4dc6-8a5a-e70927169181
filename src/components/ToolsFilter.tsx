'use client';

import { useState, useEffect } from 'react';
import { Search, Filter, X, ChevronDown } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';

interface Category {
  id: number;
  slug: string;
  level: number;
  translations: {
    name: string;
    locale: string;
  }[];
  _count: {
    tools: number;
  };
}

interface ToolsFilterProps {
  locale: string;
  className?: string;
}

export default function ToolsFilter({ locale, className }: ToolsFilterProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [searchQuery, setSearchQuery] = useState(searchParams.get('search') || '');
  const [selectedCategory, setSelectedCategory] = useState(searchParams.get('category') || '');
  const [categories, setCategories] = useState<Category[]>([]);
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  const [loading, setLoading] = useState(false);

  // 获取分类列表
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch(`/api/categories?locale=${locale}`);
        if (response.ok) {
          const data = await response.json();
          setCategories(data.categories || []);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };

    fetchCategories();
  }, [locale]);

  // 处理搜索
  const handleSearch = () => {
    setLoading(true);
    const params = new URLSearchParams();
    
    if (searchQuery.trim()) {
      params.set('search', searchQuery.trim());
    }
    
    if (selectedCategory) {
      params.set('category', selectedCategory);
    }

    const queryString = params.toString();
    const newUrl = `/${locale}/tools${queryString ? `?${queryString}` : ''}`;
    
    router.push(newUrl);
    setTimeout(() => setLoading(false), 500);
  };

  // 处理分类选择
  const handleCategorySelect = (categorySlug: string) => {
    setSelectedCategory(categorySlug);
    setShowCategoryDropdown(false);
    
    const params = new URLSearchParams();
    
    if (searchQuery.trim()) {
      params.set('search', searchQuery.trim());
    }
    
    if (categorySlug) {
      params.set('category', categorySlug);
    }

    const queryString = params.toString();
    const newUrl = `/${locale}/tools${queryString ? `?${queryString}` : ''}`;
    
    router.push(newUrl);
  };

  // 清除筛选
  const clearFilters = () => {
    setSearchQuery('');
    setSelectedCategory('');
    router.push(`/${locale}/tools`);
  };

  // 获取选中分类的名称
  const getSelectedCategoryName = () => {
    if (!selectedCategory) return '所有分类';
    
    const category = categories.find(cat => cat.slug === selectedCategory);
    if (!category) return '所有分类';
    
    const translation = category.translations.find(t => t.locale === locale) || category.translations[0];
    return translation?.name || '所有分类';
  };

  // 处理回车键搜索
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div className={cn("bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700", className)}>
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col lg:flex-row gap-4 items-center">
          {/* 搜索框 */}
          <div className="flex-1 relative">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                type="text"
                placeholder="搜索AI工具..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                className="pl-10 pr-4 h-12 text-base rounded-full border-gray-300 focus:border-primary focus:ring-primary"
              />
            </div>
          </div>

          {/* 分类筛选 */}
          <div className="relative">
            <Button
              variant="outline"
              onClick={() => setShowCategoryDropdown(!showCategoryDropdown)}
              className="h-12 px-6 rounded-full border-gray-300 hover:border-primary flex items-center gap-2 min-w-[160px] justify-between"
            >
              <div className="flex items-center gap-2">
                <Filter className="w-4 h-4" />
                <span className="truncate">{getSelectedCategoryName()}</span>
              </div>
              <ChevronDown className={cn("w-4 h-4 transition-transform", showCategoryDropdown && "rotate-180")} />
            </Button>

            <AnimatePresence>
              {showCategoryDropdown && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="absolute top-full mt-2 left-0 right-0 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto"
                >
                  <div className="p-2">
                    <button
                      onClick={() => handleCategorySelect('')}
                      className={cn(
                        "w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",
                        !selectedCategory && "bg-primary/10 text-primary"
                      )}
                    >
                      所有分类
                    </button>
                    
                    {categories.map((category) => {
                      const translation = category.translations.find(t => t.locale === locale) || category.translations[0];
                      if (!translation) return null;
                      
                      return (
                        <button
                          key={category.slug}
                          onClick={() => handleCategorySelect(category.slug)}
                          className={cn(
                            "w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center justify-between",
                            selectedCategory === category.slug && "bg-primary/10 text-primary"
                          )}
                        >
                          <span>{translation.name}</span>
                          <span className="text-xs text-gray-500">({category._count.tools})</span>
                        </button>
                      );
                    })}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* 搜索按钮 */}
          <Button
            onClick={handleSearch}
            disabled={loading}
            className="h-12 px-8 rounded-full bg-primary hover:bg-primary/90"
          >
            {loading ? (
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              '搜索'
            )}
          </Button>

          {/* 清除筛选按钮 */}
          {(searchQuery || selectedCategory) && (
            <Button
              variant="ghost"
              onClick={clearFilters}
              className="h-12 px-4 rounded-full text-gray-500 hover:text-gray-700"
            >
              <X className="w-4 h-4 mr-2" />
              清除
            </Button>
          )}
        </div>

        {/* 当前筛选状态显示 */}
        {(searchQuery || selectedCategory) && (
          <div className="mt-4 flex flex-wrap gap-2">
            {searchQuery && (
              <div className="bg-primary/10 text-primary px-3 py-1 rounded-full text-sm flex items-center gap-2">
                <span>搜索: {searchQuery}</span>
                <button
                  onClick={() => {
                    setSearchQuery('');
                    const params = new URLSearchParams();
                    if (selectedCategory) params.set('category', selectedCategory);
                    const queryString = params.toString();
                    router.push(`/${locale}/tools${queryString ? `?${queryString}` : ''}`);
                  }}
                  className="hover:bg-primary/20 rounded-full p-0.5"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            )}
            
            {selectedCategory && (
              <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm flex items-center gap-2">
                <span>分类: {getSelectedCategoryName()}</span>
                <button
                  onClick={() => {
                    setSelectedCategory('');
                    const params = new URLSearchParams();
                    if (searchQuery.trim()) params.set('search', searchQuery.trim());
                    const queryString = params.toString();
                    router.push(`/${locale}/tools${queryString ? `?${queryString}` : ''}`);
                  }}
                  className="hover:bg-blue-200 rounded-full p-0.5"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
