'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Badge } from "@/components/ui/badge";
import { ExternalLink, Star, Tag } from 'lucide-react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { useTranslations } from 'next-intl';
import { ToolScreenshot } from '@/components/SEOImage';

type ToolTranslation = {
  locale: string;
  name: string;
  description: string;
};

type Category = {
  slug: string;
  translations: ToolTranslation[];
};

type TagType = {
  slug: string;
  translations: ToolTranslation[];
};

type Tool = {
  id: number;
  toolId: string;
  iconUrl: string;
  url: string;
  pricingType: string;
  isPremium: boolean;
  isNew: boolean;
  isFeatured: boolean;
  rating?: number;
  translations: ToolTranslation[];
  publisher?: string;
  // 添加标签类型
  categories?: Category[];
  tags?: TagType[];
};

// 定义标签数据类型
interface DisplayTag {
  id: string;
  name: string;
}

interface ToolCardProps {
  tool: Tool;
  locale: string;
}

export function ToolCard({ tool, locale }: ToolCardProps) {
  // 获取国际化翻译
  const t = useTranslations('tools.toolDetail');
  
  // 安全地获取工具的名称和描述，并提供后备值
  const currentToolTranslation = tool.translations?.find(trans => trans.locale === locale) || tool.translations?.[0];

  const nameToDisplay = currentToolTranslation?.name || (tool.toolId ? `工具 "${tool.toolId}"` : `工具 ${tool.id}`);
  const descriptionToDisplay = currentToolTranslation?.description || "此工具的详细描述即将上线。";

  // 如果没有找到特定于区域设置的翻译或任何后备翻译，则记录警告
  if (!currentToolTranslation) {
    if (!tool.translations || tool.translations.length === 0) {
      console.warn(`Translations array is missing or empty for tool: ${tool.toolId || tool.id}. Using fallback name and description.`);
    } else {
      console.warn(`Locale '${locale}' translation not found, or no suitable fallback in translations for tool: ${tool.toolId || tool.id}. Using generated fallback name and description.`);
    }
  }
  
  // 图片加载状态
  const [imageLoaded, setImageLoaded] = useState(false);
  const [, setImageError] = useState(false);
  
  // 处理图片加载完成事件
  const handleImageLoad = () => {
    setImageLoaded(true);
  };
  
  // 处理图片加载错误事件
  const handleImageError = () => {
    setImageError(true);
    console.error("Image load error for:", tool.iconUrl);
  };
  
  // 处理外部链接点击，阻止事件冒泡
  const handleExternalLinkClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    window.open(tool.url, '_blank');
  };

  // 获取价格类型对应的翻译文本
  const getPricingText = (): string => {
    switch(tool.pricingType) {
      case 'freemium':
        return t('pricing.freemium');
      case 'paid':
        return t('pricing.paid');
      case 'subscription':
        return t('pricing.subscription');
      case 'free':
      default:
        // 如果pricingType是'free'或任何其他值，默认显示"free"对应的翻译
        return t('pricing.free');
    }
  };

  // 获取标签（最多3个）
  const getDisplayCategories = (): DisplayTag[] => {
    const categories: DisplayTag[] = [];
    
    // 只添加分类标签，最多3个
    if (tool.categories && tool.categories.length > 0) {
      tool.categories.slice(0, 3).forEach(category => {
        const categoryTranslation = category.translations?.find(t => t.locale === locale);
        if (categoryTranslation) {
          categories.push({
            id: `category-${category.slug}`,
            name: categoryTranslation.name
          });
        }
      });
    }
    
    return categories.slice(0, 3);
  };

  // 卡片动画变量
  const cardVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    hover: { 
      y: -5,
      scale: 1.02,
      boxShadow: "0 15px 30px -10px rgba(0, 0, 0, 0.3)"
    }
  };

  // 获取分类列表
  const displayCategories = getDisplayCategories();

  return (
    <div className="h-full">
      <motion.div 
        className={cn(
          "h-full flex flex-col relative overflow-hidden rounded-xl border border-gray-800/10 dark:border-gray-700/30",
          "bg-card dark:bg-gray-800/70 backdrop-blur-sm transition-colors",
          tool.isFeatured && "border-primary/20 dark:border-primary/30"
        )}
        initial="initial"
        animate="animate"
        whileHover="hover"
        variants={cardVariants}
        transition={{ duration: 0.3 }}
      >
        {/* 工具链接 - 包裹整个卡片内容 */}
        <Link href={`/${locale}/tools/${tool.toolId}`} className="block h-full" target="_blank" rel="noopener noreferrer">
          {/* 卡片头部：预览图 */}
          <div className="relative aspect-video w-full overflow-hidden tool-linear tool-pic skeleton-bg">
            {/* 背景渐变效果 */}
            <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/30 z-10"></div>
            
            {/* 图片加载占位符 */}
            <div className={cn(
              "absolute inset-0 flex items-center justify-center z-10",
              imageLoaded ? "opacity-0" : "opacity-100",
              "transition-opacity duration-500"
            )}>
              <div className="text-center w-full h-full relative">
                <div className="absolute inset-0 flex items-center justify-center">
                  {/* 使用重复的aistak.com文字作为背景 */}
                  <div className="absolute inset-0 grid place-items-center overflow-hidden">
                    <div className="transform -rotate-12">
                      {Array.from({ length: 10 }).map((_, i) => (
                        <div key={i} className="flex gap-2 opacity-10">
                          {Array.from({ length: 5 }).map((_, j) => (
                            <span key={j} className="text-lg font-bold text-primary whitespace-nowrap">
                              AISTAK.COM
                            </span>
                          ))}
                        </div>
                      ))}
                    </div>
                  </div>
                  {/* 中央突出的aistak.com标志 */}
                  <div className="bg-black/20 backdrop-blur-sm px-4 py-2 rounded-lg z-10 border border-white/10">
                    <span className="text-xl font-bold text-white">aistak.com</span>
                  </div>
                </div>
              </div>
            </div>
            
            {/* 工具图片 */}
            <ToolScreenshot
              tool={tool}
              locale={locale}
              className={cn(
                "el-image__inner go-tool-detail-pic",
                "object-cover transition-opacity duration-300",
                imageLoaded ? "opacity-100" : "opacity-0"
              )}
              onLoad={handleImageLoad}
              onError={handleImageError}
            />
            
            {/* 状态标签 */}
            <div className="absolute top-3 right-3 z-20 flex flex-col gap-1">
              {tool.isNew && (
                <Badge className="bg-green-600 hover:bg-green-700 text-white">{t('pricing.new', { defaultValue: '新' })}</Badge>
              )}
              {/* 修改isPremium标签显示逻辑，使用getPricingText函数获取价格类型文本 */}
              {tool.isPremium && (
                <Badge className="bg-amber-500 hover:bg-amber-600 text-white">{getPricingText()}</Badge>
              )}
            </div>
          </div>
          
          <div className="p-4 flex flex-col flex-grow relative">
            {/* 卡片标题和评分 */}
            <div className="flex items-start justify-between mb-3">
              <motion.h3 
                className="font-semibold text-lg line-clamp-1 text-gray-900 dark:text-gray-100 transition-colors"
                variants={{
                  hover: { color: 'var(--primary)' }
                }}
              >
                {nameToDisplay}
              </motion.h3>
              
              {tool.rating && (
                <div className="flex items-center gap-1 shrink-0">
                  <Star className="w-4 h-4 text-yellow-400 fill-yellow-400" />
                  <span className="text-sm text-gray-600 dark:text-gray-300">{tool.rating}</span>
                </div>
              )}
            </div>
            
            {/* 工具描述 */}
            <motion.p 
              className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2 mb-3"
            >
              {descriptionToDisplay}
            </motion.p>
            
            {/* 标签区域 */}
            {displayCategories.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-4">
                {displayCategories.map(tag => (
                  <Badge 
                    key={tag.id}
                    variant="secondary" 
                    className="text-xs flex items-center gap-1 font-normal 
                      bg-gray-100 dark:bg-gray-700 
                      text-gray-700 dark:text-gray-200 
                      hover:bg-gray-200 dark:hover:bg-gray-600"
                  >
                    <Tag className="h-3 w-3" />
                    {tag.name}
                  </Badge>
                ))}
              </div>
            )}
            
            {/* 统计数据区域 - 暂时注释掉，按照用户之前的修改 */}
            <div className="mt-auto flex justify-between items-center">
              <span className="text-sm text-primary font-medium dark:text-primary-foreground">{t('viewDetails', { defaultValue: '查看详情' })}</span>
              
              <button
                onClick={handleExternalLinkClick}
                className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <ExternalLink size={16} className="text-gray-500 dark:text-gray-400" />
              </button>
            </div>
          </div>
        </Link>
      </motion.div>
    </div>
  );
} 