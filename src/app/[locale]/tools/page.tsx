'use client';

import { useState, useEffect, useRef, Suspense } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import { ToolCard } from '@/components/ToolCard';
import { Button } from '@/components/ui/button';
import { getTools, Tool } from '@/app/actions';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import Image from 'next/image';
import { ChevronRight } from 'lucide-react';
import { prisma } from '@/lib/prisma';

// 定义工具接口，与组件兼容
interface ToolTranslation {
  name: string;
  description: string;
}

interface ToolType {
  id: number;
  toolId: string;
  iconUrl: string;
  url: string;
  pricingType: string;
  isPremium: boolean;
  isNew: boolean;
  isFeatured: boolean;
  rating: number | null;
  translations: ToolTranslation[];
  categories: {
    category: {
      id: number;
      slug: string;
      translations: {
        name: string;
      }[];
    }
  }[];
}

// 工具卡片骨架屏组件
function ToolCardSkeleton() {
  return (
    <div className="h-full rounded-lg border bg-card text-card-foreground shadow-sm animate-pulse">
      <div className="p-6 pb-2">
        <div className="flex justify-between items-start">
          <div className="w-12 h-12 bg-gray-200 rounded-md"></div>
          <div className="flex space-x-1">
            <div className="w-8 h-5 bg-gray-200 rounded-full"></div>
          </div>
        </div>
        <div className="h-6 bg-gray-200 rounded mt-4 w-3/4"></div>
        <div className="h-4 bg-gray-200 rounded mt-2 w-1/3"></div>
      </div>
      <div className="p-6 pt-0">
        <div className="h-4 bg-gray-200 rounded mt-2 w-full"></div>
        <div className="h-4 bg-gray-200 rounded mt-2 w-full"></div>
        <div className="h-4 bg-gray-200 rounded mt-2 w-2/3"></div>
      </div>
      <div className="p-6 pt-0">
        <div className="flex justify-between items-center">
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
        </div>
      </div>
    </div>
  );
}

// 工具卡片网格组件
function ToolsGrid({ tools, locale }: { tools: Tool[], locale: string }) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {tools.map((tool) => (
        <ToolCard key={tool.toolId} tool={tool} locale={locale} />
      ))}
    </div>
  );
}

// 骨架屏网格
function SkeletonGrid() {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {[...Array(8)].map((_, index) => (
        <ToolCardSkeleton key={index} />
      ))}
    </div>
  );
}

// 加载指示器组件
function LoadingIndicator() {
  return (
    <div className="w-full flex justify-center py-6">
      <div className="flex items-center space-x-2">
        <div className="w-3 h-3 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: '0ms' }}></div>
        <div className="w-3 h-3 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: '150ms' }}></div>
        <div className="w-3 h-3 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: '300ms' }}></div>
      </div>
    </div>
  );
}

// 自定义工具卡片组件
function CustomToolCard({ tool, locale }: { tool: ToolType; locale: string }) {
  // 确保有翻译数据
  if (tool.translations.length === 0) return null;
  const translation = tool.translations[0];

  return (
    <Link 
      href={`/${locale}/tools/${tool.toolId}`} 
      className="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow overflow-hidden border border-gray-200 dark:border-gray-700 flex flex-col h-full"
      target="_blank"
      rel="noopener noreferrer"
    >
      <div className="w-full h-48 relative bg-gray-100 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* 添加背景渐变效果 */}
        <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/30 z-10"></div>
        
        <Image 
          src={tool.iconUrl} 
          alt={translation.name}
          className="object-cover hover:scale-105 transition-transform duration-300"
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          placeholder="blur"
          blurDataURL="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 225' width='400' height='225'%3E%3Crect width='400' height='225' fill='%23f5f5f5'/%3E%3C/svg%3E"
        />
        
        {/* 状态标签 */}
        <div className="absolute top-3 right-3 z-20 flex flex-col gap-1">
          {tool.isNew && (
            <span className="text-xs bg-green-600 text-white rounded-full px-2 py-0.5">New</span>
          )}
          {tool.isPremium && (
            <span className="text-xs bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 rounded-full px-2 py-0.5">Premium</span>
          )}
        </div>
      </div>
      <div className="p-4 flex-grow flex flex-col">
        <h3 className="font-bold text-gray-900 dark:text-white mb-2 line-clamp-1">{translation.name}</h3>
        <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2 mb-2 flex-grow">{translation.description}</p>
        <div className="flex justify-between items-center mt-auto pt-2 border-t border-gray-100 dark:border-gray-700">
          <div className="flex items-center">
            {tool.isNew && (
              <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full px-2 py-0.5 mr-1">New</span>
            )}
            {tool.isPremium && (
              <span className="text-xs bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 rounded-full px-2 py-0.5">Premium</span>
            )}
          </div>
          <div className="flex items-center">
            {tool.rating && (
              <div className="flex items-center">
                <span className="text-yellow-400 text-sm">★</span>
                <span className="text-sm text-gray-600 dark:text-gray-400 ml-1">{tool.rating.toFixed(1)}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </Link>
  );
}

export default function ToolsListPage() {
  const { locale } = useParams<{ locale: string }>();
  const searchParams = useSearchParams();
  const categorySlug = searchParams.get('category');
  
  const t = useTranslations('tools');
  const categoryT = useTranslations('categories');
  
  const [tools, setTools] = useState<Tool[]>([]);
  const [categoryName, setCategoryName] = useState<string>("");
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 20,
    totalPages: 0,
  });
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  
  // 创建一个引用来保存观察器目标元素
  const observerTarget = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // 重置状态
    setTools([]);
    setPagination({
      total: 0,
      page: 1,
      limit: 20,
      totalPages: 0,
    });
    setLoading(true);
    setHasMore(true);
    
    // 获取分类名称
    if (categorySlug) {
      fetchCategoryName();
    }
    
    // 加载工具
    loadTools();
  }, [categorySlug, locale]);

  // 获取分类名称
  const fetchCategoryName = async () => {
    if (!categorySlug) return;
    
    try {
      const response = await fetch(`/api/categories/${categorySlug}?locale=${locale || 'en'}`);
      if (response.ok) {
        const data = await response.json();
        if (data.category && data.category.translations.length > 0) {
          setCategoryName(data.category.translations[0].name);
        }
      }
    } catch (error) {
      console.error('Error fetching category name:', error);
    }
  };

  // 设置Intersection Observer来检测滚动
  useEffect(() => {
    // 如果正在加载或者没有更多数据，不创建观察器
    if (loading || !hasMore) return;
    
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !loadingMore && pagination.page < pagination.totalPages) {
          loadTools(pagination.page + 1);
        }
      },
      { threshold: 0.1 } // 当目标元素10%可见时触发
    );
    
    const currentTarget = observerTarget.current;
    if (currentTarget) {
      observer.observe(currentTarget);
    }
    
    // 清理函数
    return () => {
      if (currentTarget) {
        observer.unobserve(currentTarget);
      }
    };
  }, [loading, loadingMore, pagination.page, pagination.totalPages, hasMore]);

  const loadTools = async (page = 1) => {
    try {
      if (page === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }
      
      // 获取排序参数
      const sort = searchParams.get('sort');
      
      let response;
      if (sort === 'newest-registered') {
        // 通过API获取按注册日期排序的工具
        const apiUrl = `/api/tools?locale=${locale || 'en'}&page=${page}&limit=20&sort=newest-registered${categorySlug ? `&category=${categorySlug}` : ''}`;
        const apiRes = await fetch(apiUrl);
        if (!apiRes.ok) {
          throw new Error('Failed to fetch tools by registration date');
        }
        response = await apiRes.json();
      } else {
        // 使用正常的getTools函数
        response = await getTools(locale || 'en', page, 20, categorySlug || undefined);
      }
      
      if (page === 1) {
        setTools(response.tools);
      } else {
        setTools(prev => [...prev, ...response.tools]);
      }
      
      setPagination(response.pagination);
      setHasMore(response.pagination.page < response.pagination.totalPages);
    } catch (error) {
      console.error('Error loading tools:', error);
      setHasMore(false);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  // 获取页面标题
  const getPageTitle = () => {
    if (categorySlug && categoryName) {
      return categoryT('allCategoryTools', { category: categoryName });
    }
    return t('allTools');
  };

  return (
    <div className="bg-gray-50 dark:bg-gray-900 min-h-screen">
      {/* 面包屑导航 */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="container mx-auto px-4 py-4">
          <div className="text-sm breadcrumbs mb-4">
            <ul className="flex items-center space-x-1">
              <li><Link href={`/${locale}`} className="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary">{categoryT('home')}</Link></li>
              <li><ChevronRight size={14} className="text-gray-400 dark:text-gray-500" /></li>
              
              {categorySlug ? (
                <>
                  <li>
                    <Link 
                      href={`/${locale}/tools`} 
                      className="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary"
                    >
                      {categoryT('tools')}
                    </Link>
                  </li>
                  <li><ChevronRight size={14} className="text-gray-400 dark:text-gray-500" /></li>
                  <li>
                    <Link 
                      href={`/${locale}/categories`} 
                      className="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary"
                    >
                      {categoryT('categoryList')}
                    </Link>
                  </li>
                  <li><ChevronRight size={14} className="text-gray-400 dark:text-gray-500" /></li>
                  <li className="text-primary font-medium">
                    {categoryName || categorySlug}
                  </li>
                </>
              ) : (
                <li className="text-primary font-medium">{t('allTools')}</li>
              )}
            </ul>
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-50">{getPageTitle()}</h1>
          {categorySlug && <p className="text-gray-600 dark:text-gray-300 mt-2">{categoryT('categoryDescription')}</p>}
        </div>
      </div>

      <div className="container mx-auto py-8 px-4">
        {loading ? (
          <SkeletonGrid />
        ) : (
          <>
            {tools.length > 0 ? (
              <>
                <Suspense fallback={<SkeletonGrid />}>
                  <ToolsGrid tools={tools} locale={locale || 'en'} />
                </Suspense>
                
                {/* 观察器目标元素 - 当滚动到此元素时加载更多 */}
                {hasMore && (
                  <div ref={observerTarget} className="mt-8">
                    {loadingMore && <LoadingIndicator />}
                  </div>
                )}
                
                {/* 全部加载完毕提示 */}
                {!hasMore && tools.length > 0 && pagination.page >= pagination.totalPages && (
                  <div className="text-center text-gray-500 mt-8 py-4">
                    {t('allLoaded')}
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-12">
                <p className="text-gray-500 dark:text-gray-400">
                  {categorySlug ? categoryT('noCategoryTools') : t('noTools')}
                </p>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
} 