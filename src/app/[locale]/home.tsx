'use client';

import { useTranslations } from 'next-intl';
import SearchTools from '@/components/SearchTools';
import { useParams } from 'next/navigation';
import { motion } from 'framer-motion';

export default function Home() {
    const t = useTranslations('Hero');
    const params = useParams();
    const locale = params.locale as string;
    
    return (
        <section className="w-full py-16 md:py-24 overflow-visible relative">
            <div className="absolute inset-0 bg-background z-0 pointer-events-none" />
            <div className="absolute inset-0 bg-[url('/images/grid.svg')] bg-center [mask-image:linear-gradient(to_bottom,white,transparent)] z-0 opacity-20 pointer-events-none" />
            
            <motion.div 
                className="container relative z-10 max-w-5xl mx-auto text-center space-y-8 px-4"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.8 }}
            >
                <motion.h1 
                    className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.2 }}
                >
                    {t('title')}
                    <span className="text-primary relative ml-2">
                        <span className="relative z-10">{t('titleHighlight')}</span>
                        <motion.span 
                            className="absolute -bottom-1 left-0 right-0 h-2 bg-primary/20 z-0 rounded-full" 
                            initial={{ width: 0 }}
                            animate={{ width: "100%" }}
                            transition={{ duration: 1, delay: 1 }}
                        />
                    </span>
                </motion.h1>
                
                <motion.p 
                    className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.4 }}
                >
                    {t('subtitle')}
                </motion.p>
                
                <motion.div
                    className="max-w-2xl mx-auto"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.6 }}
                >
                    {/* 搜索组件 */}
                    <SearchTools 
                        locale={locale} 
                        placeholder={t('searchPlaceholder')}
                        searchButtonText={t('searchButton')}
                    />
                    
                    {/* 热门分类标签 */}
                    {/* <div className="mt-5 flex flex-wrap justify-center gap-2">
                        <span className="text-sm text-gray-500 dark:text-gray-400 mr-2 self-center">{t('trending')}:</span>
                        <motion.span 
                            className="px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded-full text-sm hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors cursor-pointer"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            {t('category1')}
                        </motion.span>
                        <motion.span 
                            className="px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded-full text-sm hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors cursor-pointer"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            {t('category2')}
                        </motion.span>
                        <motion.span 
                            className="px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded-full text-sm hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors cursor-pointer"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            {t('category3')}
                        </motion.span>
                    </div> */}
                </motion.div>
            </motion.div>
        </section>
    );
}