import { getTranslations } from 'next-intl/server';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Home,
  Search,
  Grid3X3,
  Mail,
  ArrowLeft,
  FileQuestion,
  Sparkles,
  Palette,
  Wand2,
  Video,
  Mic
} from 'lucide-react';
import { routing } from '@/i18n/routing';

export default async function NotFound() {
  // For not-found pages, we'll default to the default locale
  // since we can't reliably extract it from the URL in this context
  const locale = routing.defaultLocale;

  // Get translations
  const t = await getTranslations('notFound');

  // Popular categories data
  const popularCategories = [
    {
      name: 'Text Generation',
      icon: Wand2,
      href: `/${locale}/categories/text-writing`,
      color: 'text-blue-500'
    },
    {
      name: 'Image Creation',
      icon: Palette,
      href: `/${locale}/categories/image`,
      color: 'text-purple-500'
    },
    {
      name: 'Video Tools',
      icon: Video,
      href: `/${locale}/categories/video`,
      color: 'text-green-500'
    },
    {
      name: 'Voice & Audio',
      icon: Mic,
      href: `/${locale}/categories/voice`,
      color: 'text-orange-500'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-accent/5">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto text-center">
          {/* 404 Icon and Number */}
          <div className="mb-8">
            <div className="relative inline-block">
              <div className="text-8xl md:text-9xl font-bold text-primary/20 select-none">
                404
              </div>
              <div className="absolute inset-0 flex items-center justify-center">
                <FileQuestion className="w-16 h-16 md:w-20 md:h-20 text-primary" />
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="space-y-6 mb-12">
            <h1 className="text-3xl md:text-4xl font-bold text-foreground">
              {t('heading')}
            </h1>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              {t('description')}
            </p>
          </div>

          {/* Search Section */}
          <div className="mb-12">
            <h2 className="text-xl font-semibold mb-4 text-foreground">
              {t('actions.searchTools')}
            </h2>
            <div className="max-w-2xl mx-auto">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                <Input
                  type="text"
                  placeholder={t('searchPlaceholder')}
                  className="pl-10 pr-4 py-3 h-12 text-base rounded-full border-border focus:border-primary"
                  readOnly
                />
                <Button
                  asChild
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 rounded-full px-6"
                >
                  <Link href={`/${locale}/tools`}>
                    {t('actions.searchTools')}
                  </Link>
                </Button>
              </div>
              <p className="text-sm text-muted-foreground mt-2 text-center">
                Click the button above to browse all AI tools
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-12">
            <Button asChild variant="default" size="lg" className="h-14">
              <Link href={`/${locale}`} className="flex items-center gap-2">
                <Home className="w-5 h-5" />
                {t('actions.goHome')}
              </Link>
            </Button>
            
            <Button asChild variant="outline" size="lg" className="h-14">
              <Link href={`/${locale}/tools`} className="flex items-center gap-2">
                <Grid3X3 className="w-5 h-5" />
                {t('actions.browseTools')}
              </Link>
            </Button>
            
            <Button asChild variant="outline" size="lg" className="h-14">
              <Link href={`/${locale}/categories`} className="flex items-center gap-2">
                <Sparkles className="w-5 h-5" />
                Categories
              </Link>
            </Button>
            
            <Button asChild variant="outline" size="lg" className="h-14">
              <Link href={`/${locale}#contact`} className="flex items-center gap-2">
                <Mail className="w-5 h-5" />
                {t('actions.contactSupport')}
              </Link>
            </Button>
          </div>

          {/* Popular Categories */}
          <div className="mb-12">
            <h2 className="text-xl font-semibold mb-6 text-foreground">
              {t('popularCategories')}
            </h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {popularCategories.map((category, index) => {
                const Icon = category.icon;
                return (
                  <Link
                    key={index}
                    href={category.href}
                    className="group p-6 rounded-xl border border-border bg-card hover:bg-accent/50 transition-all duration-300 hover:shadow-md hover:-translate-y-1"
                  >
                    <div className="flex flex-col items-center space-y-3">
                      <div className={`p-3 rounded-full bg-accent/20 group-hover:bg-accent/30 transition-colors ${category.color}`}>
                        <Icon className="w-6 h-6" />
                      </div>
                      <span className="text-sm font-medium text-center text-foreground group-hover:text-primary transition-colors">
                        {category.name}
                      </span>
                    </div>
                  </Link>
                );
              })}
            </div>
          </div>

          {/* Helpful Links */}
          <div className="text-center">
            <p className="text-muted-foreground mb-4">
              {t('suggestion')}
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm">
              <Link 
                href={`/${locale}`} 
                className="text-primary hover:text-primary/80 transition-colors"
              >
                Homepage
              </Link>
              <span className="text-muted-foreground">•</span>
              <Link 
                href={`/${locale}/tools`} 
                className="text-primary hover:text-primary/80 transition-colors"
              >
                AI Tools
              </Link>
              <span className="text-muted-foreground">•</span>
              <Link 
                href={`/${locale}/categories`} 
                className="text-primary hover:text-primary/80 transition-colors"
              >
                Categories
              </Link>
              <span className="text-muted-foreground">•</span>
              <Link 
                href={`/${locale}#contact`} 
                className="text-primary hover:text-primary/80 transition-colors"
              >
                Contact
              </Link>
            </div>
          </div>

          {/* Back Button - Simple link instead of client-side navigation */}
          <div className="mt-12">
            <Button asChild variant="ghost" size="lg" className="text-muted-foreground hover:text-foreground">
              <Link href={`/${locale}`}>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Go Back to Home
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
